<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revita Desktop - Chọn Phòng Khám</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- Room Selection Screen -->
        <div id="room-selection" class="screen active">
            <header class="header">
                <h1>Chọn Phòng Khám</h1>
                <p>Vui lòng chọn phòng khám phù hợp</p>
            </header>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Đang tải danh sách phòng...</p>
            </div>
            
            <div class="error" id="error" style="display: none;">
                <p id="error-message"></p>
                <button onclick="loadRooms()" class="btn btn-primary">Thử lại</button>
            </div>
            
            <div class="rooms-container" id="rooms-container" style="display: none;">
                <div class="rooms-grid" id="rooms-grid">
                    <!-- Rooms will be populated here -->
                </div>
            </div>
        </div>

        <!-- Queue Screen -->
        <div id="queue-screen" class="screen">
            <header class="header">
                <div class="room-info">
                    <button onclick="goBackToRoomSelection()" class="btn btn-secondary back-btn">← Quay lại</button>
                    <div>
                        <h1 id="selected-room-name">Phòng Khám</h1>
                        <p id="selected-doctor-name">Bác sĩ</p>
                    </div>
                    <button onclick="clearAllData()" class="btn btn-danger clear-btn">Xóa dữ liệu</button>
                </div>
            </header>

            <div class="queue-container">
                <div class="queue-status">
                    <div class="status-item">
                        <span class="status-label">Trạng thái Kafka:</span>
                        <span class="status-value" id="kafka-status">Đang kết nối...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Đang chờ:</span>
                        <span class="status-value" id="waiting-count">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Đang phục vụ:</span>
                        <span class="status-value" id="serving-count">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Đã quay lại:</span>
                        <span class="status-value" id="returned-count">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Đã qua lượt:</span>
                        <span class="status-value" id="skipped-count">0</span>
                    </div>
                </div>

                <div class="queue-layout">
                    <!-- Main Queue Table -->
                    <div class="main-queue">
                        <h3>Danh sách chờ hiện tại</h3>
                        <div class="queue-table-container">
                            <table class="queue-table">
                                <thead>
                                    <tr>
                                        <th>STT</th>
                                        <th>Tên bệnh nhân</th>
                                        <th>Mã BN</th>
                                        <th>Trạng thái</th>
                                        <th>Thời gian</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody id="main-queue-body">
                                    <tr class="empty-row">
                                        <td colspan="6">Chưa có bệnh nhân nào trong danh sách chờ</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Side Tables -->
                    <div class="side-tables">
                        <!-- Returned Patients -->
                        <div class="side-table returned-table">
                            <h4>Bệnh nhân đã quay lại</h4>
                            <div class="side-table-container">
                                <table class="queue-table compact">
                                    <thead>
                                        <tr>
                                            <th>Tên</th>
                                            <th>Mã BN</th>
                                            <th>Ưu tiên</th>
                                        </tr>
                                    </thead>
                                    <tbody id="returned-queue-body">
                                        <tr class="empty-row">
                                            <td colspan="3">Không có</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Skipped Patients -->
                        <div class="side-table skipped-table">
                            <h4>Bệnh nhân đã qua lượt</h4>
                            <div class="side-table-container">
                                <table class="queue-table compact">
                                    <thead>
                                        <tr>
                                            <th>Tên</th>
                                            <th>Mã BN</th>
                                            <th>Gọi lại sau</th>
                                        </tr>
                                    </thead>
                                    <tbody id="skipped-queue-body">
                                        <tr class="empty-row">
                                            <td colspan="3">Không có</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
