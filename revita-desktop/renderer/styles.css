* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    min-height: 100vh;
    color: #333;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.screen {
    display: none;
    flex: 1;
    padding: 20px;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.room-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    text-align: left;
    width: 100%;
}

.back-btn {
    margin-right: 20px;
}

.clear-btn {
    margin-left: auto;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    text-align: center;
    color: white;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.error p {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.rooms-container {
    flex: 1;
    overflow-y: auto;
}

.rooms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.room-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.room-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: #3b82f6;
}

.room-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.room-code {
    background: #3b82f6;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.room-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
}

.specialty {
    color: #3b82f6;
    font-weight: 600;
    margin-bottom: 16px;
}

.doctor-info {
    background: #f7fafc;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
}

.doctor-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.doctor-details {
    font-size: 0.9rem;
    color: #718096;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.services {
    margin-top: 16px;
}

.services h4 {
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 1rem;
}

.service-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.service-name {
    color: #4a5568;
}

.service-price {
    color: #3b82f6;
    font-weight: 600;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-2px);
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Queue Screen Styles */
.queue-container {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.queue-status {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.status-item {
    text-align: center;
}

.status-label {
    display: block;
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.status-value {
    display: block;
    color: #2d3748;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Queue Table Styles */
.queue-table-container {
    background: white;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    overflow: hidden;
}

.queue-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.queue-table thead {
    background: #3b82f6;
    color: white;
}

.queue-table th {
    padding: 16px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.queue-table td {
    padding: 12px;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
}

.queue-table tbody tr:hover {
    background: #f8fafc;
}

.queue-table tbody tr:last-child td {
    border-bottom: none;
}

/* Status styling */
.status-serving {
    background: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-waiting {
    background: #f59e0b;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-completed {
    background: #6b7280;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-returned {
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.patient-code {
    font-family: 'Courier New', monospace;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.empty-row td {
    text-align: center;
    color: #718096;
    padding: 40px;
    font-style: italic;
}

/* Row highlighting for current patient */
.current-patient {
    background: #dbeafe !important;
    border-left: 4px solid #3b82f6;
}

.next-patient {
    background: #fef3c7 !important;
    border-left: 4px solid #f59e0b;
}

/* Status indicators */
.status-connected {
    color: #38a169 !important;
}

.status-disconnected {
    color: #e53e3e !important;
}

.status-connecting {
    color: #d69e2e !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .rooms-grid {
        grid-template-columns: 1fr;
        padding: 0 10px;
    }
    
    .room-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .queue-status {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .queue-table-container {
        overflow-x: auto;
    }

    .queue-table {
        min-width: 600px;
    }
    
    .patient-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .patient-details {
        flex-direction: column;
        gap: 4px;
    }
}
