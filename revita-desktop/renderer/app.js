// Global state
let rooms = [];
let selectedRoom = null;
let queueData = [];

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api';

// DOM Elements
const roomSelectionScreen = document.getElementById('room-selection');
const queueScreen = document.getElementById('queue-screen');
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const errorMessage = document.getElementById('error-message');
const roomsContainer = document.getElementById('rooms-container');
const roomsGrid = document.getElementById('rooms-grid');

// Queue screen elements
const selectedRoomName = document.getElementById('selected-room-name');
const selectedDoctorName = document.getElementById('selected-doctor-name');
const kafkaStatus = document.getElementById('kafka-status');
const waitingCount = document.getElementById('waiting-count');
const servingCount = document.getElementById('serving-count');
const returnedCount = document.getElementById('returned-count');
const skippedCount = document.getElementById('skipped-count');
const mainQueueBody = document.getElementById('main-queue-body');
const returnedQueueBody = document.getElementById('returned-queue-body');
const skippedQueueBody = document.getElementById('skipped-queue-body');

// Initialize app
document.addEventListener('DOMContentLoaded', async () => {
    await loadStoredData();
    loadRooms();
    setupKafkaListener();
});

// Load rooms from API
async function loadRooms() {
    showLoading();
    
    try {
        const response = await fetch(`${API_BASE_URL}/routing/rooms`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        rooms = await response.json();
        displayRooms();
        
    } catch (err) {
        console.error('Failed to load rooms:', err);
        showError(`Không thể tải danh sách phòng: ${err.message}`);
    }
}

// Display rooms in grid
function displayRooms() {
    if (!rooms || rooms.length === 0) {
        showError('Không có phòng khám nào khả dụng');
        return;
    }
    
    roomsGrid.innerHTML = '';
    
    rooms.forEach(room => {
        const roomCard = createRoomCard(room);
        roomsGrid.appendChild(roomCard);
    });
    
    hideLoading();
    roomsContainer.style.display = 'block';
}

// Create room card element
function createRoomCard(room) {
    const card = document.createElement('div');
    card.className = 'room-card';
    card.onclick = () => selectRoom(room);
    
    const servicesHtml = room.services.map(serviceItem => `
        <div class="service-item">
            <span class="service-name">${serviceItem.service.name}</span>
            <span class="service-price">${formatPrice(serviceItem.service.price)}</span>
        </div>
    `).join('');
    
    card.innerHTML = `
        <div class="room-header">
            <div class="room-code">${room.roomCode}</div>
        </div>
        <div class="room-name">${room.roomName}</div>
        <div class="specialty">${room.specialty.name}</div>
        
        <div class="doctor-info">
            <div class="doctor-name">${room.doctor.auth.name}</div>
            <div class="doctor-details">
                <span>Kinh nghiệm: ${room.doctor.yearsExperience} năm</span>
                <span>Đánh giá: ${room.doctor.rating}/5</span>
            </div>
        </div>
        
        <div class="services">
            <h4>Dịch vụ khám:</h4>
            <div class="service-list">
                ${servicesHtml}
            </div>
        </div>
    `;
    
    return card;
}

// Select room and switch to queue screen
async function selectRoom(room) {
    selectedRoom = room;

    // Save selected room
    if (window.electronAPI) {
        await window.electronAPI.saveSelectedRoom(room);
    }

    // Update queue screen info
    selectedRoomName.textContent = room.roomName;
    selectedDoctorName.textContent = room.doctor.auth.name;

    // Switch screens
    roomSelectionScreen.classList.remove('active');
    queueScreen.classList.add('active');

    // Start Kafka listener
    await startKafkaListener(room.id);

    // Update display with existing data
    updateQueueDisplay();
}

// Go back to room selection
async function goBackToRoomSelection() {
    await stopKafkaListener();

    // Save current queue data before switching
    if (window.electronAPI) {
        await window.electronAPI.saveQueueData(queueData);
    }

    queueScreen.classList.remove('active');
    roomSelectionScreen.classList.add('active');
}

// Kafka listener setup
function setupKafkaListener() {
    if (window.electronAPI) {
        window.electronAPI.onKafkaMessage((message) => {
            console.log('Received Kafka message:', message);
            handleKafkaMessage(message);
        });
    }
}

// Start Kafka listener
async function startKafkaListener(roomId) {
    if (!window.electronAPI) {
        console.warn('Electron API not available');
        updateKafkaStatus('disconnected', 'API không khả dụng');
        return;
    }
    
    try {
        updateKafkaStatus('connecting', 'Đang kết nối...');
        
        await window.electronAPI.startKafkaListener(roomId);
        updateKafkaStatus('connected', 'Đã kết nối');
        
    } catch (err) {
        console.error('Failed to start Kafka listener:', err);
        updateKafkaStatus('disconnected', 'Lỗi kết nối');
    }
}

// Stop Kafka listener
async function stopKafkaListener() {
    if (window.electronAPI) {
        try {
            await window.electronAPI.stopKafkaListener();
            updateKafkaStatus('disconnected', 'Đã ngắt kết nối');
        } catch (err) {
            console.error('Failed to stop Kafka listener:', err);
        }
    }
}

// Handle incoming Kafka messages
function handleKafkaMessage(message) {
    if (message.type === 'PATIENT_ASSIGNED' && message.roomId === selectedRoom?.id) {
        // Add or update patient in queue
        const existingIndex = queueData.findIndex(p => p.patientProfileId === message.patientProfileId);

        if (existingIndex >= 0) {
            queueData[existingIndex] = { ...queueData[existingIndex], ...message };
        } else {
            queueData.push(message);
        }

        // Sort by event time
        queueData.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));

        updateQueueDisplay();

        // Save data
        if (window.electronAPI) {
            window.electronAPI.saveQueueData(queueData);
        }
    }
}

// Update queue display with priority algorithm
function updateQueueDisplay() {
    // Separate patients by status
    const activePatients = queueData.filter(p =>
        !['LEFT_TEMPORARILY', 'COMPLETED'].includes(p.status?.toUpperCase())
    );

    const returnedPatients = activePatients.filter(p =>
        p.status?.toUpperCase() === 'RETURNED'
    );

    const skippedPatients = activePatients.filter(p =>
        p.status?.toUpperCase() === 'SKIPPED'
    );

    // Apply priority algorithm
    const prioritizedQueue = applyPriorityAlgorithm(activePatients);

    // Update counters
    const servingPatients = activePatients.filter(p => p.status?.toUpperCase() === 'SERVING');
    const waitingPatients = activePatients.filter(p =>
        !p.status || p.status?.toUpperCase() === 'WAITING'
    );

    waitingCount.textContent = waitingPatients.length;
    servingCount.textContent = servingPatients.length;
    returnedCount.textContent = returnedPatients.length;
    skippedCount.textContent = skippedPatients.length;

    // Update main queue table
    updateMainQueueTable(prioritizedQueue);

    // Update side tables
    updateReturnedTable(returnedPatients);
    updateSkippedTable(skippedPatients);
}

// Priority algorithm for queue management
function applyPriorityAlgorithm(patients) {
    const serving = patients.filter(p => p.status?.toUpperCase() === 'SERVING');
    const returned = patients.filter(p => p.status?.toUpperCase() === 'RETURNED');
    const waiting = patients.filter(p => !p.status || p.status?.toUpperCase() === 'WAITING');
    const skipped = patients.filter(p => p.status?.toUpperCase() === 'SKIPPED');

    // Sort each group by event time
    serving.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));
    returned.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));
    waiting.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));
    skipped.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));

    // Priority order: serving -> returned (next after current) -> waiting -> skipped (after 4 turns)
    const prioritizedQueue = [];

    // Add serving patients first
    prioritizedQueue.push(...serving);

    // Add returned patients (high priority - next after serving)
    prioritizedQueue.push(...returned);

    // Add waiting patients
    prioritizedQueue.push(...waiting);

    // Add skipped patients at the end (they'll be called after 4 more patients)
    prioritizedQueue.push(...skipped);

    return prioritizedQueue;
}

// Update main queue table
function updateMainQueueTable(patients) {
    if (patients.length === 0) {
        mainQueueBody.innerHTML = `
            <tr class="empty-row">
                <td colspan="6">Chưa có bệnh nhân nào trong danh sách chờ</td>
            </tr>
        `;
        return;
    }

    const tableRows = patients.map((patient, index) => {
        const status = getPatientStatus(patient.status);
        const rowClass = getRowClass(patient.status, index);
        const actions = getActionButtons(patient);

        return `
            <tr class="${rowClass}">
                <td><strong>${index + 1}</strong></td>
                <td>${patient.patientName || `Bệnh nhân #${patient.patientProfileId.slice(-8)}`}</td>
                <td><span class="patient-code">${patient.patientProfileId.slice(-8)}</span></td>
                <td><span class="status-${status.class}">${status.text}</span></td>
                <td>${formatTime(patient.eventTime)}</td>
                <td>${actions}</td>
            </tr>
        `;
    }).join('');

    mainQueueBody.innerHTML = tableRows;
}

// Update returned patients table
function updateReturnedTable(patients) {
    if (patients.length === 0) {
        returnedQueueBody.innerHTML = `
            <tr class="empty-row">
                <td colspan="3">Không có</td>
            </tr>
        `;
        return;
    }

    const tableRows = patients.map((patient, index) => `
        <tr class="returned-patient">
            <td>${patient.patientName || `BN #${patient.patientProfileId.slice(-8)}`}</td>
            <td><span class="patient-code">${patient.patientProfileId.slice(-8)}</span></td>
            <td><span class="priority-badge">Ưu tiên</span></td>
        </tr>
    `).join('');

    returnedQueueBody.innerHTML = tableRows;
}

// Update skipped patients table
function updateSkippedTable(patients) {
    if (patients.length === 0) {
        skippedQueueBody.innerHTML = `
            <tr class="empty-row">
                <td colspan="3">Không có</td>
            </tr>
        `;
        return;
    }

    const tableRows = patients.map((patient, index) => `
        <tr>
            <td>${patient.patientName || `BN #${patient.patientProfileId.slice(-8)}`}</td>
            <td><span class="patient-code">${patient.patientProfileId.slice(-8)}</span></td>
            <td><span class="call-after-badge">Sau 4 lượt</span></td>
        </tr>
    `).join('');

    skippedQueueBody.innerHTML = tableRows;
}

// Get patient status with styling
function getPatientStatus(status) {
    const upperStatus = status?.toUpperCase();
    switch (upperStatus) {
        case 'SERVING':
            return { class: 'serving', text: 'Đang khám' };
        case 'COMPLETED':
            return { class: 'completed', text: 'Hoàn thành' };
        case 'RETURNED':
            return { class: 'returned', text: 'Đã quay lại' };
        case 'SKIPPED':
            return { class: 'skipped', text: 'Đã qua lượt' };
        case 'LEFT_TEMPORARILY':
            return { class: 'left-temporarily', text: 'Tạm rời' };
        case 'WAITING':
        default:
            return { class: 'waiting', text: 'Chờ khám' };
    }
}

// Get row class for highlighting
function getRowClass(status, index) {
    const upperStatus = status?.toUpperCase();
    if (upperStatus === 'SERVING') {
        return 'current-patient';
    }
    if (upperStatus === 'RETURNED') {
        return 'returned-patient';
    }
    if ((!status || upperStatus === 'WAITING') && index === 0) {
        return 'next-patient';
    }
    return '';
}

// Get action buttons for patient
function getActionButtons(patient) {
    const upperStatus = patient.status?.toUpperCase();
    const patientId = patient.patientProfileId;

    let buttons = [];

    if (!upperStatus || upperStatus === 'WAITING') {
        buttons.push(`<button class="btn-action btn-serving" onclick="updatePatientStatus('${patientId}', 'SERVING')">Phục vụ</button>`);
        buttons.push(`<button class="btn-action btn-skip" onclick="updatePatientStatus('${patientId}', 'SKIPPED')">Qua lượt</button>`);
    } else if (upperStatus === 'SERVING') {
        buttons.push(`<button class="btn-action btn-complete" onclick="updatePatientStatus('${patientId}', 'COMPLETED')">Hoàn thành</button>`);
        buttons.push(`<button class="btn-action btn-return" onclick="updatePatientStatus('${patientId}', 'RETURNED')">Quay lại</button>`);
    } else if (upperStatus === 'RETURNED') {
        buttons.push(`<button class="btn-action btn-serving" onclick="updatePatientStatus('${patientId}', 'SERVING')">Phục vụ</button>`);
    } else if (upperStatus === 'SKIPPED') {
        buttons.push(`<button class="btn-action btn-return" onclick="updatePatientStatus('${patientId}', 'RETURNED')">Gọi lại</button>`);
    }

    return `<div class="action-buttons">${buttons.join('')}</div>`;
}

// Update patient status via API
async function updatePatientStatus(patientId, newStatus) {
    if (!selectedRoom) {
        alert('Không có phòng nào được chọn!');
        return;
    }

    try {
        const endpoint = getStatusEndpoint(newStatus);
        const response = await fetch(`${API_BASE_URL}/routing/status/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                patientProfileId: patientId,
                roomId: selectedRoom.id
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Update local data
        const patientIndex = queueData.findIndex(p => p.patientProfileId === patientId);
        if (patientIndex >= 0) {
            queueData[patientIndex].status = newStatus;
            updateQueueDisplay();

            // Save updated data
            if (window.electronAPI) {
                await window.electronAPI.saveQueueData(queueData);
            }
        }

    } catch (err) {
        console.error('Failed to update patient status:', err);
        alert(`Không thể cập nhật trạng thái: ${err.message}`);
    }
}

// Get API endpoint for status
function getStatusEndpoint(status) {
    switch (status.toUpperCase()) {
        case 'SERVING':
            return 'serving';
        case 'COMPLETED':
            return 'completed';
        case 'RETURNED':
            return 'returned';
        case 'SKIPPED':
            return 'skipped';
        case 'LEFT_TEMPORARILY':
            return 'left-temporarily';
        default:
            return 'serving';
    }
}

// Update Kafka connection status
function updateKafkaStatus(status, message) {
    kafkaStatus.textContent = message;
    kafkaStatus.className = `status-value status-${status}`;
}

// Utility functions
function showLoading() {
    loading.style.display = 'flex';
    error.style.display = 'none';
    roomsContainer.style.display = 'none';
}

function hideLoading() {
    loading.style.display = 'none';
}

function showError(message) {
    hideLoading();
    errorMessage.textContent = message;
    error.style.display = 'flex';
    roomsContainer.style.display = 'none';
}

function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(price);
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        day: '2-digit',
        month: '2-digit'
    });
}

// Load stored data on app start
async function loadStoredData() {
    if (!window.electronAPI) return;

    try {
        // Load selected room
        const storedRoom = await window.electronAPI.loadSelectedRoom();
        if (storedRoom) {
            selectedRoom = storedRoom;

            // Load queue data for this room
            const storedQueueData = await window.electronAPI.loadQueueData();
            if (storedQueueData && Array.isArray(storedQueueData)) {
                queueData = storedQueueData;
            }

            // Switch to queue screen if we have a selected room
            selectedRoomName.textContent = storedRoom.roomName;
            selectedDoctorName.textContent = storedRoom.doctor.auth.name;

            roomSelectionScreen.classList.remove('active');
            queueScreen.classList.add('active');

            updateQueueDisplay();

            // Start Kafka listener for the stored room
            await startKafkaListener(storedRoom.id);
        }
    } catch (err) {
        console.error('Failed to load stored data:', err);
    }
}

// Clear all stored data
async function clearAllData() {
    if (!window.electronAPI) return;

    const confirmed = confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu đã lưu?');
    if (!confirmed) return;

    try {
        await window.electronAPI.clearStoredData();

        // Reset local state
        selectedRoom = null;
        queueData = [];

        // Stop Kafka listener
        await stopKafkaListener();

        // Switch back to room selection
        queueScreen.classList.remove('active');
        roomSelectionScreen.classList.add('active');

        alert('Đã xóa tất cả dữ liệu thành công!');

    } catch (err) {
        console.error('Failed to clear data:', err);
        alert('Có lỗi xảy ra khi xóa dữ liệu!');
    }
}

// Export functions for global access
window.loadRooms = loadRooms;
window.selectRoom = selectRoom;
window.goBackToRoomSelection = goBackToRoomSelection;
window.clearAllData = clearAllData;
window.updatePatientStatus = updatePatientStatus;
