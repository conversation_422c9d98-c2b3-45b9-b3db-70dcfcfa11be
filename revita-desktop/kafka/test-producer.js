/*
  Test Kafka producer to simulate patient assignments
  Usage: node kafka/test-producer.js <ROOM_ID>
*/

const { Kafka } = require('kafkajs');

async function main() {
  const roomId = process.argv[2];
  if (!roomId) {
    console.error('Missing argument: ROOM_ID');
    console.error('Example: node kafka/test-producer.js <ROOM_ID>');
    process.exit(1);
  }

  const brokers = (process.env.KAFKA_BROKERS || 'localhost:9092')
    .split(',')
    .map((b) => b.trim())
    .filter(Boolean);
  const topic = process.env.KAFKA_TOPIC_ASSIGNMENTS || 'clinic.assignments';

  const kafka = new Kafka({ clientId: 'revita-test-producer', brokers });
  const producer = kafka.producer();

  await producer.connect();

  // Sample patient data with new statuses
  const patients = [
    { name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>', status: 'WAITING' },
    { name: '<PERSON>r<PERSON><PERSON> Thị B', status: 'SERVING' },
    { name: '<PERSON><PERSON>ăn <PERSON>', status: 'WAITING' },
    { name: '<PERSON><PERSON><PERSON> Thị D', status: 'RETURNED' },
    { name: 'Hoàng Văn E', status: 'SKIPPED' },
    { name: 'Võ Thị F', status: 'WAITING' },
    { name: 'Đặng Văn G', status: 'LEFT_TEMPORARILY' },
    { name: 'Bùi Thị H', status: 'COMPLETED' }
  ];

  console.log(`Sending test messages to topic "${topic}" for roomId=${roomId}`);

  for (let i = 0; i < patients.length; i++) {
    const patient = patients[i];
    const message = {
      type: 'PATIENT_ASSIGNED',
      patientProfileId: `patient-${i + 1}-${Date.now()}`,
      patientName: patient.name,
      status: patient.status,
      roomId: roomId,
      roomCode: 'MAT-01',
      doctorId: 'doctor-uuid',
      doctorCode: 'DOC_MAT_01',
      serviceIds: ['service-uuid-1'],
      timestamp: new Date().toISOString()
    };

    await producer.send({
      topic,
      messages: [
        {
          key: message.patientProfileId,
          value: JSON.stringify(message)
        }
      ]
    });

    console.log(`Sent: ${patient.name} (${patient.status})`);
    
    // Wait 2 seconds between messages
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  await producer.disconnect();
  console.log('Test completed');
}

main().catch((err) => {
  console.error('Test producer failed:', err);
  process.exit(1);
});
