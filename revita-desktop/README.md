# Revita Desktop

Ứng dụng desktop cho hệ thống quản lý phòng khám Revita, đ<PERSON><PERSON><PERSON> xây dựng bằng Electron.

## Tính năng

- **Chọn phòng khám**: Hiển thị danh sách các phòng khám từ API và cho phép người dùng chọn phòng
- **<PERSON>h sách chờ**: Hiển thị danh sách bệnh nhân chờ khám theo thời gian thực thông qua Kafka
- **Lưu trữ dữ liệu**: Tự động lưu trạng thái khi thoát app và khôi phục khi mở lại
- **Giao diện bảng**: Hiển thị danh sách chờ dạng bảng với các trạng thái rõ ràng
- **Quản lý trạng thái**: <PERSON><PERSON> loại bệnh nhân theo trạng thái (đang kh<PERSON>, chờ kh<PERSON>, ho<PERSON><PERSON>à<PERSON>, quay lại)
- **Giao diện hiện đại**: Thi<PERSON><PERSON> kế với màu blue-500, responsive và dễ sử dụng

## Cài đặt

1. Cài đặt dependencies:
```bash
npm install
```

2. Chạy ứng dụng trong chế độ development:
```bash
npm run dev
```

3. Chạy ứng dụng production:
```bash
npm start
```

4. Build ứng dụng:
```bash
npm run build
```

## Cấu hình

### API Configuration
Ứng dụng sẽ gọi API tại `http://localhost:3000/api/routing/rooms` để lấy danh sách phòng khám.

### Kafka Configuration
Cấu hình Kafka thông qua biến môi trường:

- `KAFKA_BROKERS`: Danh sách Kafka brokers (mặc định: `localhost:9092`)
- `KAFKA_TOPIC_ASSIGNMENTS`: Topic để lắng nghe assignments (mặc định: `clinic.assignments`)

Ví dụ:
```bash
KAFKA_BROKERS=localhost:9092 KAFKA_TOPIC_ASSIGNMENTS=clinic.assignments npm start
```

## Cấu trúc dự án

```
revita-desktop/
├── main.js                 # Main Electron process
├── package.json            # Dependencies và scripts
├── renderer/               # Renderer process files
│   ├── index.html         # Main HTML file
│   ├── styles.css         # Styles
│   ├── app.js            # Main application logic
│   └── preload.js        # Preload script for IPC
├── kafka/                 # Kafka related files
│   └── room-listener.js   # Kafka consumer for room assignments
└── README.md              # This file
```

## API Response Format

Ứng dụng expect API `/routing/rooms` trả về format như sau:

```json
[
  {
    "id": "room-uuid",
    "roomCode": "MAT-01",
    "roomName": "Phòng Mắt 01",
    "specialty": {
      "name": "Mắt"
    },
    "doctor": {
      "auth": {
        "name": "Bác sĩ Mắt 01"
      },
      "yearsExperience": 9,
      "rating": 4.5
    },
    "services": [
      {
        "service": {
          "name": "Khám mắt tổng quát",
          "price": 150000
        }
      }
    ]
  }
]
```

## Kafka Message Format

Kafka messages cho patient assignments:

```json
{
  "type": "PATIENT_ASSIGNED",
  "patientProfileId": "patient-uuid",
  "patientName": "Tên bệnh nhân",
  "status": "waiting",
  "roomId": "room-uuid",
  "roomCode": "MAT-01",
  "doctorId": "doctor-uuid",
  "doctorCode": "DOC_MAT_01",
  "serviceIds": ["service-uuid"],
  "timestamp": "2025-08-18T07:10:15.458Z"
}
```

## Troubleshooting

### Không thể kết nối API
- Kiểm tra API server đang chạy tại `http://localhost:3000`
- Kiểm tra endpoint `/api/routing/rooms` có hoạt động

### Không nhận được Kafka messages
- Kiểm tra Kafka server đang chạy
- Kiểm tra topic `clinic.assignments` đã được tạo
- Kiểm tra biến môi trường `KAFKA_BROKERS` và `KAFKA_TOPIC_ASSIGNMENTS`

### Lỗi build
- Đảm bảo đã cài đặt đầy đủ dependencies: `npm install`
- Kiểm tra phiên bản Node.js (khuyến nghị >= 16.x)
